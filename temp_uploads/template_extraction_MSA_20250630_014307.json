{"analysis_type": "template_based_extraction", "template_used": "Master Service Agreement", "template_id": "9650d9c4-7c00-4c06-a334-75ef5d389514", "template_system_prompt": "You are a highly intelligent legal AI assistant specializing in contract analysis. Your task is to meticulously review the provided Master Service Agreement text and extract the specific fields listed below. Adhere strictly to the definitions for each field. If a field or its corresponding information is not present in the text, you must explicitly state 'Not Found'. Your final output must be a valid JSON object.", "template_fields": [{"field_name": "Party 1", "prompt": "Identify and state the full legal name of the first party to the agreement, often referred to as the 'Service Provider' or 'Consultant'."}, {"field_name": "Party 2", "prompt": "Identify and state the full legal name of the second party to the agreement, often referred to as the 'Client' or 'Customer'."}, {"field_name": "Effective Date", "prompt": "Extract the specific date on which the agreement becomes effective. This might be at the beginning of the document or near the signature lines."}, {"field_name": "Payment Terms", "prompt": "Extract the complete clause or section that describes the payment schedule (e.g., Net 30, Net 60), invoicing process, and any late payment penalties."}, {"field_name": "Term of Agreement", "prompt": "Identify and quote the clause that specifies the initial duration of the agreement and any conditions for automatic renewal."}, {"field_name": "Termination Clause", "prompt": "Extract the entire clause detailing the conditions under which either party can terminate the agreement, including required notice periods (e.g., 'termination for convenience' or 'termination for cause')."}, {"field_name": "Governing Law", "prompt": "Identify the state, province, or country whose laws will govern the interpretation and enforcement of the agreement."}, {"field_name": "Limitation of Liability", "prompt": "Extract the clause that limits the financial liability of one or both parties, often capped at the amount of fees paid over a certain period."}], "timestamp": "2025-06-30T01:43:07.687237", "result": {"Party 1": "Alcon Vision, LLC", "Party 2": "Starcom Worldwide, Inc.", "Effective Date": "January 1, 2022", "Payment Terms": "Not Found", "Term of Agreement": "Not Found", "Termination Clause": "Not Found", "Governing Law": "Not Found", "Limitation of Liability": "Not Found"}}