[{"contract_type": "Master Service Agreement", "template_name": "msa_v1", "description": "Extracts key commercial and legal terms from a Master Service Agreement. Use this for comprehensive service contracts between two corporate entities.", "system_prompt": "You are a highly intelligent legal AI assistant specializing in contract analysis. Your task is to meticulously review the provided Master Service Agreement text and extract the specific fields listed below. Adhere strictly to the definitions for each field. If a field or its corresponding information is not present in the text, you must explicitly state 'Not Found'. Your final output must be a valid JSON object.", "fields_to_extract": [{"field_name": "Party 1", "prompt": "Identify and state the full legal name of the first party to the agreement, often referred to as the 'Service Provider' or 'Consultant'."}, {"field_name": "Party 2", "prompt": "Identify and state the full legal name of the second party to the agreement, often referred to as the 'Client' or 'Customer'."}, {"field_name": "Effective Date", "prompt": "Extract the specific date on which the agreement becomes effective. This might be at the beginning of the document or near the signature lines."}, {"field_name": "Payment Terms", "prompt": "Extract the complete clause or section that describes the payment schedule (e.g., Net 30, Net 60), invoicing process, and any late payment penalties."}, {"field_name": "Term of Agreement", "prompt": "Identify and quote the clause that specifies the initial duration of the agreement and any conditions for automatic renewal."}, {"field_name": "Termination Clause", "prompt": "Extract the entire clause detailing the conditions under which either party can terminate the agreement, including required notice periods (e.g., 'termination for convenience' or 'termination for cause')."}, {"field_name": "Governing Law", "prompt": "Identify the state, province, or country whose laws will govern the interpretation and enforcement of the agreement."}, {"field_name": "Limitation of Liability", "prompt": "Extract the clause that limits the financial liability of one or both parties, often capped at the amount of fees paid over a certain period."}]}, {"contract_type": "Statement of Work (SOW)", "template_name": "sow_v1", "description": "Extracts project-specific details from a Statement of Work document, including deliverables, timelines, and payment schedules.", "system_prompt": "You are a project management AI assistant. Your purpose is to parse the provided Statement of Work (SOW) and extract key project details into a structured JSON format. Focus on concrete deliverables, dates, and costs. If a field is not mentioned, state 'Not Found'.", "fields_to_extract": [{"field_name": "Project Name", "prompt": "Identify the official name or title of the project as stated in the SOW."}, {"field_name": "Parties", "prompt": "Identify the names of the client and the service provider involved in this SOW."}, {"field_name": "Period of Performance", "prompt": "Extract the start and end dates for the project work defined in this SOW."}, {"field_name": "Scope of Work", "prompt": "Provide a detailed summary or quote the entire 'Scope of Work' section, which describes the work to be performed."}, {"field_name": "Key Deliverables", "prompt": "List all specific deliverables that the service provider is committed to producing, along with their due dates if specified."}, {"field_name": "Payment Schedule", "prompt": "Extract the payment amounts and the milestones or dates at which they are due. This could be a fixed fee, time and materials, or milestone-based payments."}, {"field_name": "Acceptance Criteria", "prompt": "Extract the criteria that will be used to judge if the deliverables are acceptable to the client."}]}, {"contract_type": "Independent Contractor Agreement", "template_name": "ica_v1", "description": "Extracts key terms defining the relationship between a company and an independent contractor, focusing on status, payment, and intellectual property.", "system_prompt": "You are an HR compliance AI assistant. Your role is to analyze the following Independent Contractor Agreement and extract the specified fields. Pay close attention to clauses that define the nature of the working relationship. Output the results as a clean JSON object. If a field is absent, respond with 'Not Found'.", "fields_to_extract": [{"field_name": "Client", "prompt": "Identify the full legal name of the company or individual hiring the contractor."}, {"field_name": "Contractor", "prompt": "Identify the full legal name of the independent contractor."}, {"field_name": "Services", "prompt": "Extract the section that describes the specific services the contractor will provide."}, {"field_name": "Compensation", "prompt": "Detail the payment terms, including the rate (e.g., per hour, per project), payment schedule, and invoicing instructions."}, {"field_name": "Independent Contractor Status", "prompt": "Extract the entire clause that explicitly states the contractor is an independent contractor and not an employee, and is responsible for their own taxes."}, {"field_name": "Intellectual Property Ownership", "prompt": "Extract the clause that defines who owns the work product or intellectual property created by the contractor during the engagement (often called 'Work for Hire')."}, {"field_name": "Term and Termination", "prompt": "Identify the start date, end date (if any), and the conditions under which either party can terminate the agreement."}]}, {"contract_type": "Non-Disclosure Agreement", "template_name": "nda_v1", "description": "Extracts critical information from a standard Non-Disclosure Agreement (NDA), focusing on parties, term, and the definition of confidential data.", "system_prompt": "You are a specialized AI paralegal. Your function is to analyze the following Non-Disclosure Agreement (NDA) and extract the defined fields. Be precise and return the data in a clean JSON format. If a specific clause or piece of information is absent, respond with 'Not Found' for that field.", "fields_to_extract": [{"field_name": "Disclosing Party", "prompt": "Identify the full name and address of the party that is disclosing confidential information."}, {"field_name": "Receiving Party", "prompt": "Identify the full name and address of the party that is receiving confidential information."}, {"field_name": "Definition of Confidential Information", "prompt": "Extract the full paragraph that defines what constitutes 'Confidential Information' under this agreement. This often includes lists of types of information (e.g., financial, business plans, customer lists)."}, {"field_name": "Term of Confidentiality", "prompt": "Identify and quote the duration (e.g., '3 years from the date of disclosure') for which the receiving party must keep the information confidential."}, {"field_name": "Permitted Use", "prompt": "Extract the clause that specifies the sole purpose for which the confidential information can be used by the receiving party (e.g., 'for the purpose of evaluating a potential business relationship')."}, {"field_name": "Return of Information", "prompt": "Find the clause that describes the Receiving Party's obligation to return or destroy the confidential information upon termination of the agreement or request from the Disclosing Party."}]}]